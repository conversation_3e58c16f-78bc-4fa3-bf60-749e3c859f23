import numpy as np

from collections import Counter
import pandas as pd
import random

class ID3DecisionTree:
    """
    ID3 (Iterative Dichotomiser 3)

    - Only categorical features.
    - Information gain (IG) as the splitting criterion.
    """

    def __init__(self, max_depth=3, feature_names=None):
        self.max_depth = max_depth
        self.tree = None
        self.feature_names = feature_names

    def _entropy(self, y):
        """Calculate entropy of a target variable"""
        if len(y) == 0:
            return 0

        counts = Counter(y)
        total = len(y)
        entropy = 0

        for count in counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)

        return entropy

    def _information_gain(self, y, y_subsets):
        """Calculate information gain for a split"""
        parent_entropy = self._entropy(y)
        total_samples = len(y)

        weighted_entropy = 0
        for subset in y_subsets:
            if len(subset) > 0:
                weight = len(subset) / total_samples
                weighted_entropy += weight * self._entropy(subset)

        return parent_entropy - weighted_entropy

    def _find_best_split(self, x, y):
        """Find the best categorical feature to split on"""
        best_gain = 0
        best_feature = None
        best_split = None

        n_features = x.shape[1]

        for feature in range(n_features):
            # Get unique values for this feature
            unique_values = np.unique(x[:, feature])

            # Create subsets based on each unique value
            y_subsets = []
            for value in unique_values:
                mask = x[:, feature] == value
                y_subsets.append(y[mask])

            # Calculate information gain
            gain = self._information_gain(y, y_subsets)

            if gain > best_gain:
                best_gain = gain
                best_feature = feature
                best_split = unique_values

        return best_feature, best_split, best_gain

    def _find_best_numeric_split(self, x, y, feature):
        # Sjekk om kolonnen er numerisk
        try:
            x_float = x[:, feature].astype(float)
        except (ValueError, TypeError):
            return None, 0  # Ikke numerisk, returner None

        # Bruk x_float for å håndtere NaN
        unique_values = np.unique(x_float[~np.isnan(x_float)])
        if len(unique_values) < 2:
            return None, 0

        sorted_values = np.sort(unique_values)
        thresholds = [(sorted_values[i] + sorted_values[i + 1]) / 2 for i in range(len(sorted_values) - 1)]

        best_threshold = None
        best_gain_ratio = 0

        for thresh in thresholds:
            gain_ratio = self._gain_ratio(x, y, feature, threshold=thresh)
            if gain_ratio > best_gain_ratio:
                best_gain_ratio = gain_ratio
                best_threshold = thresh

        return best_threshold, best_gain_ratio

    def _create_leaf(self, y):
        """Create a leaf node with the most common class"""
        most_common = Counter(y).most_common(1)[0][0]
        return {'type': 'leaf', 'prediction': most_common}

    def _build_tree(self, x, y, depth=0):
        # Stoppkriterier
        if (depth >= self.max_depth or
                len(np.unique(y)) == 1 or
                len(y) == 0):
            return self._create_leaf(y)

        # Finn beste split med C4.5
        best_feature, best_split, best_gain = self._find_best_splitc45(x, y)

        # Hvis ingen god split, lag blad
        if best_gain == 0:
            return self._create_leaf(y)

        # Opprett subtrær
        subtrees = {}
        if isinstance(best_split, (float, np.float64)):
            # Numerisk split
            try:
                x_float = x[:, best_feature].astype(float)
            except (ValueError, TypeError):
                return self._create_leaf(y)  # Ikke numerisk, lag blad
            valid_mask = ~np.isnan(x_float)
            valid_x = x[valid_mask]
            valid_y = y[valid_mask]
            left_mask = x_float[valid_mask] <= best_split
            right_mask = x_float[valid_mask] > best_split
            subtrees['<='] = self._build_tree(valid_x[left_mask], valid_y[left_mask], depth + 1)
            subtrees['>'] = self._build_tree(valid_x[right_mask], valid_y[right_mask], depth + 1)
            # Håndter NaN
            if np.any(~valid_mask):
                subtrees['NaN'] = self._create_leaf(y[~valid_mask])
        else:
            # Kategorisk split
            for value in best_split:
                mask = x[:, best_feature] == value
                x_subset = x[mask]
                y_subset = y[mask]
                if len(y_subset) > 0:
                    subtrees[value] = self._build_tree(x_subset, y_subset, depth + 1)
                else:
                    subtrees[value] = self._create_leaf(y)

        return {
            'type': 'decision',
            'feature': best_feature,
            'subtrees': subtrees,
            'threshold': best_split if isinstance(best_split, (float, np.float64)) else None
        }

    def fit(self, x, y):
        """Train the ID3 decision tree"""
        x = np.array(x)
        y = np.array(y)
        self.tree = self._build_tree(x, y)
        return self

    def _predict_single(self, x, node):
        if node['type'] == 'leaf':
            return node['prediction']

        feature_value = x[node['feature']]
        if node['threshold'] is not None:
            # Numerisk split
            if isinstance(feature_value, float) and np.isnan(feature_value):
                if 'NaN' in node['subtrees']:
                    return self._predict_single(x, node['subtrees']['NaN'])
                first_subtree = list(node['subtrees'].values())[0]
                return self._predict_single(x, first_subtree)
            try:
                feature_value = float(feature_value)
                if feature_value <= node['threshold']:
                    return self._predict_single(x, node['subtrees']['<='])
                else:
                    return self._predict_single(x, node['subtrees']['>'])
            except (ValueError, TypeError):
                if 'NaN' in node['subtrees']:
                    return self._predict_single(x, node['subtrees']['NaN'])
                first_subtree = list(node['subtrees'].values())[0]
                return self._predict_single(x, first_subtree)
        else:
            # Kategorisk split
            if feature_value in node['subtrees']:
                return self._predict_single(x, node['subtrees'][feature_value])
            else:
                first_subtree = list(node['subtrees'].values())[0]
                return self._predict_single(x, first_subtree)

    def predict(self, x):
        """Make predictions for multiple samples"""
        x = np.array(x)
        predictions = []
        for sample in x:
            predictions.append(self._predict_single(sample, self.tree))
        return np.array(predictions)

    def print_tree(self, node=None, depth=0, prefix=""):
        if node is None:
            node = self.tree

        if node['type'] == 'leaf':
            print(f"{prefix}└── Predict: {node['prediction']}")
        else:
            feature_name = self._get_feature_name(node['feature'])

            for i, (value, subtree) in enumerate(node['subtrees'].items()):
                if node['threshold'] is not None:
                    # Numerisk split
                    if value == '<=':
                        print(
                            f"{prefix}{'└──' if i == len(node['subtrees']) - 1 else '├──'} {feature_name} <= {node['threshold']}")
                    elif value == '>':
                        print(
                            f"{prefix}{'└──' if i == len(node['subtrees']) - 1 else '├──'} {feature_name} > {node['threshold']}")
                    else:
                        print(
                            f"{prefix}{'└──' if i == len(node['subtrees']) - 1 else '├──'} {feature_name} = {value}")
                else:
                    # Kategorisk split
                    print(
                        f"{prefix}{'└──' if i == len(node['subtrees']) - 1 else '├──'} {feature_name} = {value}")
                self.print_tree(subtree, depth + 1, prefix + ("    " if i == len(node['subtrees']) - 1 else "│   "))

    def _get_feature_name(self, feature_index):
        """Get feature name or return Feature X format"""
        if self.feature_names and feature_index < len(self.feature_names):
            return f"Feature {feature_index} : {self.feature_names[feature_index]}"
        else:
            return f"Feature {feature_index}"

    def _split_information(self, x, feature, threshold=None):
        total_samples = len(x)
        if total_samples == 0:
            return 0

        # Sjekk om x er 1D eller 2D
        is_1d = x.ndim == 1
        x_feature = x if is_1d else x[:, feature]

        if threshold is None:
            # Kategorisk
            counts = Counter(x_feature)
            split_info = 0
            for count in counts.values():
                p = count / total_samples
                if p > 0:
                    split_info -= p * np.log2(p)
        else:
            # Numerisk
            try:
                x_float = x_feature.astype(float)
            except (ValueError, TypeError):
                return 0  # Ikke numerisk, returner 0
            valid_mask = ~np.isnan(x_float)
            valid_x = x_float[valid_mask]
            left_mask = valid_x <= threshold
            right_mask = valid_x > threshold
            left_count = np.sum(left_mask)
            right_count = np.sum(right_mask)
            total = left_count + right_count
            if total == 0:
                return 0
            split_info = -sum((count / total) * np.log2(count / total)
                              for count in [left_count, right_count] if count > 0)

        return split_info

    def _gain_ratio(self, x, y, feature, threshold=None):
        if threshold is None:
            # Kategorisk
            unique_values = np.unique(x[:, feature])
            y_subsets = [y[x[:, feature] == value] for value in unique_values]
            info_gain = self._information_gain(y, y_subsets)
        else:
            # Numerisk
            try:
                x_float = x[:, feature].astype(float)
            except (ValueError, TypeError):
                return 0  # Ikke numerisk, returner 0
            valid_mask = ~np.isnan(x_float)
            valid_x = x_float[valid_mask]
            valid_y = y[valid_mask]
            left_y = valid_y[valid_x <= threshold]
            right_y = valid_y[valid_x > threshold]
            info_gain = self._information_gain(valid_y, [left_y, right_y])

        # Beregn split information
        split_info = self._split_information(x, feature, threshold)

        # Unngå divisjon med null
        if split_info == 0:
            return 0

        return info_gain / split_info

    '''def _gain_ratio(self, x, y, feature):
        """Beregn gain ratio for en feature"""
        # Beregn information gain ved å bruke eksisterende funksjon
        unique_values = np.unique(x[:, feature])
        y_subsets = [y[x[:, feature] == value] for value in unique_values]
        info_gain = self._information_gain(y, y_subsets)

        # Beregn split information
        split_info = self._split_information(x, feature)

        # Unngå divisjon med null
        if split_info == 0:
            return 0

        # Beregn gain ratio
        return info_gain / split_info'''

    def _find_best_splitc45(self, x, y):
        best_gain_ratio = 0
        best_feature = None
        best_split = None
        numeric_features = [1, 3]  # Fare, Age

        n_features = x.shape[1]
        if n_features == 0:
            return None, None, 0

        max_features = max(1, int(np.sqrt(n_features)))
        random_features = np.random.choice(n_features, max_features, replace=False)


        for feature in random_features:
            # Kategorisk split
            unique_values = np.unique(x[:, feature])
            gain_ratio = self._gain_ratio(x, y, feature)
            if gain_ratio > best_gain_ratio:
                best_gain_ratio = gain_ratio
                best_feature = feature
                best_split = unique_values

            # Numerisk split (hvis feature er numerisk)
            if feature in numeric_features:
                threshold, numeric_gain_ratio = self._find_best_numeric_split(x, y, feature)
                if numeric_gain_ratio > best_gain_ratio:
                    best_gain_ratio = numeric_gain_ratio
                    best_feature = feature
                    best_split = threshold  # En terskel, ikke liste med verdier

        return best_feature, best_split, best_gain_ratio


df = pd.read_csv('titanic.csv')


def preprocess_data(df1):
    df1 = df1.drop(['PassengerId', 'Name', 'Ticket', 'Cabin', 'Embarked', 'Survived'], axis=1)
    df1['Sex'] = df1['Sex'].map({'male': 'male', 'female': 'female'}).astype(str)
    # Pclass, SibSp, Parch beholdes som int64 (kategoriske)
    # Age, Fare beholdes som float64, ingen NaN-fylling
    return df1


"""def preprocess_data(df1):
    df1 = df1.drop(['PassengerId','Embarked', 'Name', 'Ticket', 'Cabin'], axis=1)
    df1['Sex'].astype(str)
    #sort df1 on age
    sorted_df = df1.sort_values(by=['Age'])
    age_treshold_list = []
    for i in range(len(sorted_df) - 1):
        age_i = sorted_df.iloc[i]['Age']
        age_i_1 = sorted_df.iloc[i + 1]['Age']
        survived_i = sorted_df.iloc[i]['Survived']
        survived_next = sorted_df.iloc[i+1]['Survived']

        if not pd.isna(age_i) and not pd.isna(age_i_1):
            if survived_i != survived_next:
                threshold = (age_i + age_i_1) / 2
                age_treshold_list.append(threshold)
    return df1"""


def train_test_split(x, y, test_size=0.2, random_state=42):
    n_samples = len(y)
    np.random.seed(random_state)
    indices = np.random.permutation(n_samples)
    test_size = int(test_size * n_samples)
    train_idx, test_idx = indices[test_size:], indices[:test_size]
    x_train, x_test = x[train_idx], x[test_idx]
    y_train, y_test = y[train_idx], y[test_idx]
    return x_train, x_test, y_train, y_test


X = preprocess_data(df)
y = df['Survived'].values



X_train, X_test, y_train, y_test = train_test_split(X.values, y, test_size=0.2, random_state=42)

np.random.seed(42)
feature_names = list(X.columns)
bootstrap_tree = []
y_hat_list = []
MaxDepth = 20
for _ in range(100):
    indices = np.random.choice(len(X_train), size=len(X_train), replace=True)
    tree = ID3DecisionTree(max_depth=MaxDepth, feature_names=feature_names)
    tree.fit(X_train[indices], y_train[indices])
    bootstrap_tree.append(tree)
    y_hat_list.append(tree.predict(X_test))


# Majority voting: for each test sample, take the most common prediction among all trees
y_hat_array = np.array(y_hat_list)  # shape: (n_trees, n_test_samples)
final_predictions = []
for col in y_hat_array.T:
    most_common = Counter(col).most_common(1)[0][0]
    final_predictions.append(most_common)
final_predictions = np.array(final_predictions)



print("\nPredictions vs Actual")
for i, (pred, actual) in enumerate(zip(final_predictions, y_test)):
    status = "✓" if pred == actual else "✗"
    print(f"Sample {i + 1:2d}: Predicted={pred}, Actual={actual} {status}")



# Calculate accuracy
accuracy = np.mean(final_predictions == y_test)
print(f"\n C4.5 Random Forest Accuracy: {accuracy:.2f}")




TP = np.sum((final_predictions == 1) & (y_test == 1))  # True Positives
TN = np.sum((final_predictions == 0) & (y_test == 0))  # True Negatives
FP = np.sum((final_predictions == 1) & (y_test == 0))  # False Positives
FN = np.sum((final_predictions == 0) & (y_test == 1))  # False Negatives


Precision = TP / (TP + FP) if (TP + FP) > 0 else 0
Recall = TP / (TP + FN) if (TP + FN) > 0 else 0
F1_score = 2 * (Precision * Recall) / (Precision + Recall) if (Precision + Recall) > 0 else 0

print(f"Precision: {Precision:.2f}")
print(f"Recall: {Recall:.2f}")
print(f"F1-score: {F1_score:.2f}")

# CSV export
correct_predictions = np.sum(final_predictions == y_test)
results_data = {
    'Tree': ['C4.5_RF'],
    'Max_depth': [MaxDepth],
    'Correct_predictions/179': [f"{correct_predictions}/179"],
    'Accuracy': [accuracy],
    'Precision': [Precision],
    'Recall': [Recall],
    'F1_score': [F1_score]
}
results_df = pd.DataFrame(results_data)

# Check if combination already exists
import os
file_exists = os.path.exists('decision_tree_results.csv')
should_append = True

if file_exists:
    existing_df = pd.read_csv('decision_tree_results.csv')
    if ((existing_df['Tree'] == 'C4.5_RF') & (existing_df['Max_depth'] == MaxDepth)).any():
        print(f"Results for C4.5_RF with max_depth={MaxDepth} already exist. Skipping append.")
        should_append = False

if should_append:
    results_df.to_csv('decision_tree_results.csv', index=False, mode='a', header=not file_exists)
    print(f"Results appended for C4.5_RF with max_depth={MaxDepth}")
