import numpy as np
from collections import Counter

class ID3DecisionTree:
    """
    ID3 (Iterative Dichotomiser 3)

    - Only categorical features.
    - Information gain (IG) as the splitting criterion.
    """
    def __init__(self, max_depth=3):
        self.max_depth = max_depth
        self.tree = None
    
    def _entropy(self, y):
        """Calculate entropy of a target variable"""
        if len(y) == 0:
            return 0
        
        counts = Counter(y)
        total = len(y)
        entropy = 0
        
        for count in counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy
    
    def _information_gain(self, y, y_subsets):
        """Calculate information gain for a split"""
        parent_entropy = self._entropy(y)
        total_samples = len(y)
        
        weighted_entropy = 0
        for subset in y_subsets:
            if len(subset) > 0:
                weight = len(subset) / total_samples
                weighted_entropy += weight * self._entropy(subset)
        
        return parent_entropy - weighted_entropy
    
    def _find_best_split(self, X, y):
        """Find the best categorical feature to split on"""
        best_gain = 0
        best_feature = None
        best_split = None
        
        n_features = X.shape[1]
        
        for feature in range(n_features):
            # Get unique values for this feature
            unique_values = np.unique(X[:, feature])
            
            # Create subsets based on each unique value
            y_subsets = []
            for value in unique_values:
                mask = X[:, feature] == value
                y_subsets.append(y[mask])
            
            # Calculate information gain
            gain = self._information_gain(y, y_subsets)
            
            if gain > best_gain:
                best_gain = gain
                best_feature = feature
                best_split = unique_values
        
        return best_feature, best_split, best_gain
    
    def _create_leaf(self, y):
        """Create a leaf node with the most common class"""
        most_common = Counter(y).most_common(1)[0][0]
        return {'type': 'leaf', 'prediction': most_common}
    
    def _build_tree(self, X, y, depth=0):
        """Recursively build the ID3 decision tree"""
        # Check stopping criteria
        if (depth >= self.max_depth or 
            len(np.unique(y)) == 1 or
            len(y) == 0):
            return self._create_leaf(y)
        
        # Find best split
        best_feature, best_split, best_gain = self._find_best_split(X, y)
        
        # If no good split found, create leaf
        if best_gain == 0:
            return self._create_leaf(y)
        
        # Create subtrees for each value of the best feature
        subtrees = {}
        for value in best_split:
            mask = X[:, best_feature] == value
            X_subset = X[mask]
            y_subset = y[mask]
            
            if len(y_subset) > 0:
                subtrees[value] = self._build_tree(X_subset, y_subset, depth + 1)
            else:
                subtrees[value] = self._create_leaf(y)
        
        return {
            'type': 'decision',
            'feature': best_feature,
            'subtrees': subtrees
        }
    
    def fit(self, X, y):
        """Train the ID3 decision tree"""
        X = np.array(X)
        y = np.array(y)
        self.tree = self._build_tree(X, y)
        return self
    
    def _predict_single(self, x, node):
        """Predict for a single sample"""
        if node['type'] == 'leaf':
            return node['prediction']
        
        feature_value = x[node['feature']]
        if feature_value in node['subtrees']:
            return self._predict_single(x, node['subtrees'][feature_value])
        else:
            # Handle unseen values by returning most common prediction
            predictions = [subtree['prediction'] for subtree in node['subtrees'].values() 
                         if subtree['type'] == 'leaf']
            if predictions:
                return Counter(predictions).most_common(1)[0][0]
            return 0  # Default fallback
    
    def predict(self, X):
        """Make predictions for multiple samples"""
        X = np.array(X)
        predictions = []
        for x in X:
            predictions.append(self._predict_single(x, self.tree))
        return np.array(predictions)
    
    def print_tree(self, node=None, depth=0, prefix=""):
        """Print the tree structure"""
        if node is None:
            node = self.tree
        
        if node['type'] == 'leaf':
            print(f"{prefix}└── Predict: {node['prediction']}")
        else:
            for i, (value, subtree) in enumerate(node['subtrees'].items()):
                if i == len(node['subtrees']) - 1:
                    print(f"{prefix}└── Feature {node['feature']} = {value}")
                    self.print_tree(subtree, depth + 1, prefix + "    ")
                else:
                    print(f"{prefix}├── Feature {node['feature']} = {value}")
                    self.print_tree(subtree, depth + 1, prefix + "│   ")


# Example usage with categorical data
if __name__ == "__main__":
    print("=== ID3 Decision Tree - Categorical Features Only ===")
    print("=" * 60)
    
    # Weather dataset with categorical features
    # Features: [outlook, temperature, humidity, windy]
    X = np.array([
        ['sunny', 'hot', 'high', 'false'],     # no rain
        ['sunny', 'hot', 'high', 'true'],      # no rain  
        ['overcast', 'hot', 'high', 'false'],  # rain
        ['rainy', 'mild', 'high', 'false'],    # rain
        ['rainy', 'cool', 'normal', 'false'],  # rain
        ['rainy', 'cool', 'normal', 'true'],   # no rain
        ['overcast', 'cool', 'normal', 'true'], # rain
        ['sunny', 'mild', 'high', 'false'],    # no rain
        ['sunny', 'cool', 'normal', 'false'],  # rain
        ['rainy', 'mild', 'normal', 'false'],  # rain
        ['sunny', 'mild', 'normal', 'true'],   # rain
        ['overcast', 'mild', 'high', 'true'],  # rain
        ['overcast', 'hot', 'normal', 'false'], # rain
        ['rainy', 'mild', 'high', 'true'],     # no rain
    ])
    
    y = np.array([0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0])
    
    print("Dataset:")
    print("Features: [outlook, temperature, humidity, windy]")
    print("Target: 0 = no rain, 1 = rain")
    print(f"Number of samples: {len(X)}")
    print()

    # Create and train the ID3 tree
    tree = ID3DecisionTree(max_depth=4)
    tree.fit(X, y)
    
    print("ID3 Decision Tree Structure:")
    print("(Each branch represents a categorical value)")
    tree.print_tree()
    print()
    
    # Make predictions
    predictions = tree.predict(X)
    accuracy = np.mean(predictions == y)
    
    print("Predictions vs Actual:")
    for i, (pred, actual) in enumerate(zip(predictions, y)):
        status = "✓" if pred == actual else "✗"
        print(f"Sample {i+1:2d}: Predicted={pred}, Actual={actual} {status}")
    
    print(f"\nAccuracy: {accuracy:.2f}")

