import os
import sys
import subprocess
from pathlib import Path

# Ensure we run from the assignment_2_decisionTree directory
BASE_DIR = Path(__file__).parent.resolve()
os.chdir(BASE_DIR)

# List of depths to sweep
DEPTHS = [5, 7, 8, 9, 10, 12, 14, 16, 18, 20]

# Scripts to run for each depth (run as separate processes)
SCRIPTS = [
    "ass2_1.py",
    "ass2_2.py",
    "ass2_3.1.py",
    "ass2_4.py",
]


def run_script(script: str, depth: int):
    print("\n" + "=" * 80)
    print(f"Running {script} with GLOBAL_MAX_DEPTH={depth}")
    print("=" * 80)
    env = os.environ.copy()
    env["GLOBAL_MAX_DEPTH"] = str(depth)
    cmd = [sys.executable, str(BASE_DIR / script)]
    try:
        result = subprocess.run(cmd, env=env)
        if result.returncode != 0:
            print(f"{script} exited with code {result.returncode}")
    except Exception as e:
        print(f"Error running {script}: {e}")


if __name__ == "__main__":
    print(f"Depth sweep: {DEPTHS}")
    for d in DEPTHS:
        print("\n" + "#" * 80)
        print(f"Depth {d}")
        print("#" * 80)
        for script in SCRIPTS:
            run_script(script, d)
