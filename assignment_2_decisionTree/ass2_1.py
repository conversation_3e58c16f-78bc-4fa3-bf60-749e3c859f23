import numpy as np
import os
from collections import Counter
import pandas as pd


class ID3DecisionTree:
    """
    ID3 (Iterative Dichotomiser 3)

    - Only categorical features.
    - Information gain (IG) as the splitting criterion.
    """

    def __init__(self, max_depth=3, feature_names=None):
        self.max_depth = max_depth
        self.tree = None
        self.feature_names = feature_names

    def _entropy(self, y):
        """Calculate entropy of a target variable"""
        if len(y) == 0:
            return 0

        counts = Counter(y)
        total = len(y)
        entropy = 0

        for count in counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)

        return entropy

    def _information_gain(self, y, y_subsets):
        """Calculate information gain for a split"""
        parent_entropy = self._entropy(y)
        total_samples = len(y)

        weighted_entropy = 0
        for subset in y_subsets:
            if len(subset) > 0:
                weight = len(subset) / total_samples
                weighted_entropy += weight * self._entropy(subset)

        return parent_entropy - weighted_entropy

    def _find_best_split(self, x, y):
        """Find the best categorical feature to split on"""
        best_gain = 0
        best_feature = None
        best_split = None

        n_features = x.shape[1]

        for feature in range(n_features):
            # Get unique values for this feature
            unique_values = np.unique(x[:, feature])

            # Create subsets based on each unique value
            y_subsets = []
            for value in unique_values:
                mask = x[:, feature] == value
                y_subsets.append(y[mask])

            # Calculate information gain
            gain = self._information_gain(y, y_subsets)

            if gain > best_gain:
                best_gain = gain
                best_feature = feature
                best_split = unique_values

        return best_feature, best_split, best_gain

    def _create_leaf(self, y):
        """Create a leaf node with the most common class"""
        most_common = Counter(y).most_common(1)[0][0]
        return {'type': 'leaf', 'prediction': most_common}

    def _build_tree(self, x, y, depth=0):
        """Recursively build the ID3 decision tree"""
        # Check stopping criteria
        if (depth >= self.max_depth or
                len(np.unique(y)) == 1 or
                len(y) == 0):
            return self._create_leaf(y)

        # Find best split
        best_feature, best_split, best_gain = self._find_best_split(x, y)

        # If no good split found, create leaf
        if best_gain == 0:
            return self._create_leaf(y)

        # Create subtrees for each value of the best feature
        subtrees = {}
        for value in best_split:
            mask = x[:, best_feature] == value
            x_subset = x[mask]
            y_subset = y[mask]

            if len(y_subset) > 0:
                subtrees[value] = self._build_tree(x_subset, y_subset, depth + 1)
            else:
                subtrees[value] = self._create_leaf(y)

        return {
            'type': 'decision',
            'feature': best_feature,
            'subtrees': subtrees
        }

    def fit(self, x, y):
        """Train the ID3 decision tree"""
        x = np.array(x)
        y = np.array(y)
        self.tree = self._build_tree(x, y)
        return self

    def _predict_single(self, x, node):
        """Predict for a single sample"""
        if node['type'] == 'leaf':
            return node['prediction']

        feature_value = x[node['feature']]
        if feature_value in node['subtrees']:
            return self._predict_single(x, node['subtrees'][feature_value])
        else:
            # Handle unseen values by returning most common prediction
            predictions = [subtree['prediction'] for subtree in node['subtrees'].values()
                            if subtree['type'] == 'leaf']
            if predictions:
                return Counter(predictions).most_common(1)[0][0]
            return 0  # Default fallback

    def predict(self, x):
        """Make predictions for multiple samples"""
        x = np.array(x)
        predictions = []
        for sample in x:
            predictions.append(self._predict_single(sample, self.tree))
        return np.array(predictions)

    def print_tree(self, node=None, depth=0, prefix=""):
        """Print the tree structure"""
        if node is None:
            node = self.tree

        if node['type'] == 'leaf':
            print(f"{prefix}└── Predict: {node['prediction']}")
        else:
            feature_name = self._get_feature_name(node['feature'])

            for i, (value, subtree) in enumerate(node['subtrees'].items()):
                if i == len(node['subtrees']) - 1:
                    print(f"{prefix}└── {feature_name} = {value}")
                    self.print_tree(subtree, depth + 1, prefix + "    ")
                else:
                    print(f"{prefix}├── {feature_name} = {value}")
                    self.print_tree(subtree, depth + 1, prefix + "│   ")

    def _get_feature_name(self, feature_index):
        """Get feature name or return Feature X format"""
        if self.feature_names and feature_index < len(self.feature_names):
            return f"Feature {feature_index} : {self.feature_names[feature_index]}"
        else:
            return f"Feature {feature_index}"


# Pandas for databehandling
df = pd.read_csv('titanic.csv')


def preprocess_data(df1):
    df1 = df1.drop(['PassengerId','Embarked', 'Name', 'Ticket', 'Cabin', 'Survived'], axis=1)
    ageBin = [0, 12, 19, 39, 59, 120]
    ageLabel = ['child', 'teen', 'adult', 'middle-aged', 'senior']
    df1['Age'] = pd.cut(df1['Age'], bins=ageBin, labels=ageLabel, include_lowest=True, right=False)
    df1['Age'] = df1['Age'].cat.add_categories('missing').fillna('missing').astype(str)

    fareBin = [0, 10, 25, 50, 75, 100, 150, 200, np.inf]
    fareLabel = ['0-10', '10-25', '25-50', '50-75', '75-100', '100-150', '150-200', '200+']
    df1['Fare'] = pd.cut(df1['Fare'], bins=fareBin, labels=fareLabel, include_lowest=True, right=False)
    df1['Fare'] = df1['Fare'].astype(str)

    # Convert all features to strings and fill missing values with 'missing'
    for col in df1.columns:
        df1[col] = df1[col].astype(str)
        df1[col] = df1[col].replace('nan', 'missing')
    return df1

def train_test_split(x, y, test_size=0.2, random_state=42):
    n_samples = len(y)
    np.random.seed(random_state)
    indices = np.random.permutation(n_samples)
    test_size = int(test_size * n_samples)
    train_idx, test_idx = indices[test_size:], indices[:test_size]
    x_train, x_test = x[train_idx], x[test_idx]
    y_train, y_test = y[train_idx], y[test_idx]
    return x_train, x_test, y_train, y_test

# Save survived before we process data.
Y = df['Survived'].values

X = preprocess_data(df)
print(X.dtypes)

X_array = X.values
X_train, X_test, Y_train, Y_test = train_test_split(X_array, Y, test_size=0.2, random_state=42)

feature_names = list(X.columns)
MaxDepth = int(os.environ.get("GLOBAL_MAX_DEPTH", "3"))
tree = ID3DecisionTree(max_depth=MaxDepth, feature_names=feature_names)
tree.fit(X_train, Y_train)

# Make predictions
y_hat = tree.predict(X_test)
accuracy = np.mean(y_hat == Y_test)

tree.print_tree()

print("Predictions vs Actual:")
for i, (pred, actual) in enumerate(zip(y_hat, Y_test)):
    status = "✓" if pred == actual else "✗"
    print(f"Sample {i + 1:2d}: Predicted={pred}, Actual={actual} {status}")

print(f"\n ID 3 Decision Tree Accuracy: {accuracy:.2f}")

TP = np.sum((y_hat == 1) & (Y_test == 1))  # True Positives
TN = np.sum((y_hat == 0) & (Y_test == 0))  # True Negatives
FP = np.sum((y_hat == 1) & (Y_test == 0))  # False Positives
FN = np.sum((y_hat == 0) & (Y_test == 1))  # False Negatives

Precision = TP / (TP + FP) if (TP + FP) > 0 else 0
Recall = TP / (TP + FN) if (TP + FN) > 0 else 0
F1_score = 2 * (Precision * Recall) / (Precision + Recall) if (Precision + Recall) > 0 else 0

print(f"Precision: {Precision:.2f}")
print(f"Recall: {Recall:.2f}")
print(f"F1-score: {F1_score:.2f}")

# CSV export
correct_predictions = np.sum(y_hat == Y_test)
results_data = {
    'Tree': ['ID_3'],
    'Max_depth': [MaxDepth],
    'Correct_predictions/179': [f"{correct_predictions}/179"],
    'Accuracy': [accuracy],
    'Precision': [Precision],
    'Recall': [Recall],
    'F1_score': [F1_score]
}
columns_order = ['Tree', 'Correct_predictions/179', 'Max_depth', 'Accuracy', 'Precision', 'Recall', 'F1_score']
results_df = pd.DataFrame(results_data)[columns_order]

# Check if file exists and if combination already exists
import os
file_exists = os.path.exists('decision_tree_results.csv')
should_append = True

if file_exists:
    existing_df = pd.read_csv('decision_tree_results.csv')
    if ((existing_df['Tree'] == 'ID_3') & (existing_df['Max_depth'] == MaxDepth)).any():
        print(f"Results for ID_3 with max_depth={MaxDepth} already exist. Skipping append.")
        should_append = False

if should_append:
    results_df.to_csv('decision_tree_results.csv', index=False, mode='a', header=not file_exists)
    print(f"Results appended for ID_3 with max_depth={MaxDepth}")
