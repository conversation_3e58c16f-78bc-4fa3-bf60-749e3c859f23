from sklearn.datasets import fetch_20newsgroups
import numpy as np
from collections import Counter


# 1. Last inn data. Vi laster inn HELE treningssettet ('train' subset er standard)
newsgroups_data = fetch_20newsgroups(subset='train')

priors = Counter(label for _, label in newsgroups_data.data)


y_texts = newsgroups_data.data # 11314 Tekster
category_number = newsgroups_data.target # kategori-nummer for hvert dokument
catergory_name = newsgroups_data.target_names # Kategorinavn

print("############First Text############\n",y_texts[0])
print(f"Første dokuments kategorinavn: {catergory_name[category_number[0]]}\n")

def get_category_name(Text_number):
    category_number = newsgroups_data.target[Text_number]
    return catergory_name[category_number]

def get_category_number(Text_number):
    return newsgroups_data.target[Text_number]

# P(kategori | tekst) = P(tekst | kategori) × P(kategori) / P(tekst)


def get_unique_words(texts):
    unique_words = set()
    for text in texts:
        words = text.split()
        unique_words.update(words)
    return unique_words

rows = len(y_texts) # 11314
cols = len(get_unique_words(y_texts)) # 280308
matrix = [[0 for _ in range(cols)] for _ in range(rows)]

print(matrix[0][0])