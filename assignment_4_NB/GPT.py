from collections import Counter
from sklearn.datasets import fetch_20newsgroups
import numpy as np
from tqdm import tqdm

print("Laster inn data ...", flush=True)
newsgroups_data = fetch_20newsgroups(subset='train')

texts = newsgroups_data.data        # dokumentene (råtekst)
labels = newsgroups_data.target     # kategorinumre
categories = newsgroups_data.target_names

print(f"Antall dokumenter: {len(texts)}")
print(f"Antall kategorier: {len(categories)}")

# Bruk kun et mindre subset for testing
print("Bruker kun de første 1000 dokumentene for testing...")
texts = texts[:1000]
labels = labels[:1000]

vocab = set() # set er unike elementer

print("Bygger vokabular ...", flush=True)
for text in tqdm(texts, desc="Prosesserer dokumenter"):
    # Enkel tekstbehandling: fjern spesialtegn og gjør til lowercase
    words = text.lower().split() # Splitter teksten på hvert ord
    # Filtrer bort ord som er for korte eller inneholder tall/spesialtegn
    filtered_words = [word for word in words if len(word) > 2 and word.isalpha()]
    vocab.update(filtered_words) # Legger til ordene i settet

vocab = sorted(vocab) # Sorterer ordene etter alfabetet
vocab_index = {word: i for i, word in enumerate(vocab)}  # Ord til indeks [eksempel Abc = 0, ban = 1 osv...]

print(f"Vokabularstørrelse: {len(vocab)} ord")

# matrise
rows = len(texts)
cols = len(vocab)


X = np.zeros((rows, cols), dtype=int)

# Tell antall forekomster av hvert ord i hvert dokument
print("Fyller feature-matrise...")
for i, text in tqdm(enumerate(texts), desc="Prosesserer dokumenter", total=len(texts)):
    # Samme tekstbehandling som over
    words = text.lower().split()
    filtered_words = [word for word in words if len(word) > 2 and word.isalpha()]
    word_counts = Counter(filtered_words)
    for word, count in word_counts.items():
        if word in vocab_index:
            j = vocab_index[word]
            X[i, j] = count

print("Matriseform:", X.shape)

# P(kategori | tekst) = P(tekst | kategori) × P(kategori) / P(tekst)

priors = Counter(labels) # teller antall forekomster av hvert label
prior_percentages = {label: count / len(labels) for label, count in priors.items()} # deler antall forekomster av hvert label med antall dokumenter

for label, percentage in prior_percentages.items():     # Printer ut prior for hvert label
    print(f"P({categories[label]}) = {percentage:.4f}")