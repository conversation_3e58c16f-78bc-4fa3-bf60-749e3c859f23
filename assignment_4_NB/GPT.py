from collections import Counter
from sklearn.datasets import fetch_20newsgroups
import numpy as np
from tqdm import tqdm

print("Laster inn data ...", flush=True)
newsgroups_data = fetch_20newsgroups(subset='train')

texts = newsgroups_data.data        # dokumentene (råtekst)
labels = newsgroups_data.target     # kategorinumre
categories = newsgroups_data.target_names

vocab = set() # set er unike elementer

print("Bygger vokabular ...", flush=True)
for text in tqdm(texts):
    words = text.split() # Splitter teksten på hvert ord
    vocab.update(words) # Legger til ordene i settet

vocab = sorted(vocab) # Sorterer ordene etter alfabetet
vocab_index = {word: i for i, word in enumerate(vocab)}  # Ord til indeks [eksempel Abc = 0, ban = 1 osv...]

print(f"Vokabularstørrelse: {len(vocab)} ord")

# matrise
rows = len(texts)
cols = len(vocab)


X = np.zeros((rows, cols), dtype=int)

# Tell antall forekomster av hvert ord i hvert dokument
for i, text in tqdm(enumerate(texts)):
    word_counts = Counter(text.split())
    for word, count in word_counts.items():
        if word in vocab_index:
            j = vocab_index[word]
            X[i, j] = count

print("Matriseform:", X.shape)

# P(kategori | tekst) = P(tekst | kategori) × P(kategori) / P(tekst)

priors = Counter(labels) # teller antall forekomster av hvert label
prior_percentages = {label: count / len(labels) for label, count in priors.items()} # deler antall forekomster av hvert label med antall dokumenter

for label, percentage in prior_percentages.items():     # Printer ut prior for hvert label
    print(f"P({categories[label]}) = {percentage:.4f}")