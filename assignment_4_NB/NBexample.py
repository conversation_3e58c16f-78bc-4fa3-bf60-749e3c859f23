from collections import Counter

data = [
    ("<PERSON>", "No"),
    ("<PERSON>", "No"),
    ("Overcast", "Yes"),
    ("<PERSON><PERSON>", "Yes"),
    ("<PERSON>", "No"),
    ("Overcast", "No"),
    ("<PERSON>y", "No"),
    ("<PERSON><PERSON>", "Yes")
]

priors = Counter(label for _, label in data)


prior_percentages = {label: count / len(data) for label, count in priors.items()}

print("Priors:", prior_percentages)

# Get the likelihoods

from collections import defaultdict

likelihoods = defaultdict(Counter)

for feature, label in data:
    likelihoods[label][feature] += 1

likelihood_percentages = {label: {feature: count / priors[label] for feature, count in likelihoods[label].items()} for
                          label in likelihoods}

print("Likelihoods:", likelihood_percentages)

# get the posterior probabilities

# Get the probability of a feature

features = defaultdict()

for feature, label in data:
    if feature not in features:
        features[feature] = 0
    features[feature] += 1

print("Features:", features)

feature_percentages = {feature: count / len(data) for feature, count in features.items()}

print("Feature Percentages:", feature_percentages)


def smoothed_prob(feature, label, k=3):
    return (likelihoods[label][feature] + 1) / (priors[label] + k)


k = 3


def predict(feature):
    scores = {}
    for label in priors:
        # prior
        score = priors[label] / len(data)
        # likelihood
        if likelihoods[label][feature] == 0:
            score *= smoothed_prob(feature, label, k=k)
        else:
            score *= likelihoods[label][feature] / priors[
                label]  # label = Yes --> score = score * 1/3 | label = No --> score = score * 2/3
        scores[label] = score

    return scores


print("Sunny:", predict("Sunny"))
print("Rainy:", predict("Rainy"))
print("Overcast:", predict("Overcast"))

# Get the label with the highest score

highest_label_score = max(predict("Sunny").values())

print("Highest Label Score:", highest_label_score)

for label in predict("Sunny"):
    if predict("Sunny")[label] == highest_label_score:
        print("Highest Label:", label)
        break


