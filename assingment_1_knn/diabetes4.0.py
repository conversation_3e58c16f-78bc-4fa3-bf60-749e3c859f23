import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm

data = np.loadtxt('pima-indians-diabetes.csv', delimiter=',')

# Random shuffle instead of sequential split
np.random.seed(42)
shuffled_indices = np.random.permutation(len(data))
data_shuffled = data[shuffled_indices]

train_size = 500
y_train = data_shuffled[:train_size, 8]
x_train = data_shuffled[:train_size, 0:8]
x_test = data_shuffled[train_size:, 0:8]
y_test = data_shuffled[train_size:, 8]


# Evaluation functions
def confusion_matrix(y_true, y_pred):
    tp = np.sum((y_true == 1) & (y_pred == 1))
    tn = np.sum((y_true == 0) & (y_pred == 0))
    fp = np.sum((y_true == 0) & (y_pred == 1))
    fn = np.sum((y_true == 1) & (y_pred == 0))
    return tp, tn, fp, fn


def calculate_metrics(y_true, y_pred):
    tp, tn, fp, fn = confusion_matrix(y_true, y_pred)

    accuracy = (tp + tn) / (tp + tn + fp + fn)
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    mse = np.mean((y_true - y_pred) ** 2)

    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'mse': mse,
        'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
    }


#Finner alle oddetall fra 1 til 50
arr = np.array(range(1,50))
odd_mask = (arr % 2) == 1
k_values = arr[odd_mask]
results = []

for k in k_values:
    print(f"Testing k={k}")
    predictions = []

    for i in tqdm(range(len(x_test))):
        distances = []
        for j in range(len(x_train)):
            diff = x_test[i] - x_train[j]
            dist = np.sqrt(np.dot(diff, diff))
            distances.append((j, dist))

        distances.sort(key=lambda x: x[1])
        k_nearest = distances[:k]

        votes = sum(1 for neighbor in k_nearest if y_train[neighbor[0]] == 1)
        prediction = 1 if votes > k / 2 else 0
        predictions.append(prediction)

    # Calculate all metrics
    metrics = calculate_metrics(y_test, np.array(predictions))
    metrics['k'] = k
    results.append(metrics)

    print(f"k={k}: Acc={metrics['accuracy']:.4f}, F1={metrics['f1_score']:.4f}, "
          f"Prec={metrics['precision']:.4f}, Rec={metrics['recall']:.4f}")

# Create results DataFrame
results_df = pd.DataFrame(results)

# Print confusion matrix for best k
best_k_idx = np.argmax(results_df['f1_score'])
best_k = results_df.iloc[best_k_idx]
print(f"\nBest k={best_k['k']} (highest F1-score):")
print(f"Confusion Matrix:")
print(f"TN: {best_k['tn']}, FP: {best_k['fp']}")
print(f"FN: {best_k['fn']}, TP: {best_k['tp']}")

# Plot accuracy and loss graphs
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# Accuracy graph
ax1.plot(results_df['k'], results_df['accuracy'], 'b-o', label='Accuracy')
ax1.plot(results_df['k'], results_df['f1_score'], 'r-s', label='F1-Score')
ax1.set_xlabel('k Value')
ax1.set_ylabel('Score')
ax1.set_title('Accuracy and F1-Score vs k')
ax1.legend()
ax1.grid(True)

# Loss (MSE) graph
ax2.plot(results_df['k'], results_df['mse'], 'g-^', label='MSE')
ax2.set_xlabel('k Value')
ax2.set_ylabel('Mean Squared Error')
ax2.set_title('Loss (MSE) vs k')
ax2.legend()
ax2.grid(True)

plt.tight_layout()
plt.savefig('knn_evaluation.png', dpi=300, bbox_inches='tight')
plt.show()

# Save detailed results
results_df.to_csv('knn_detailed_results.csv', index=False)
print(f"\nDetailed results saved to knn_detailed_results.csv")
print(results_df)