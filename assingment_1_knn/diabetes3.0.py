import numpy as np
import pandas as pd
from tqdm import tqdm

data = np.loadtxt('pima-indians-diabetes.csv', delimiter=',')

#<PERSON>er alle oddetall fra 1 til 50
arr = np.array(range(1,50))
odd_mask = (arr % 2) == 1
k_values = arr[odd_mask]

results = []

np.random.seed(42)
shuffled_indices = np.random.permutation(len(data))
data_shuffled = data[shuffled_indices]

# Now split the shuffled data
train_size = 500
train_idx = list(range(train_size))
test_idx = list(range(train_size, len(data_shuffled)))

y_train = data[train_idx, 8]
x_train = data[train_idx, 0:8]
x_test = data[test_idx, 0:8]
y_test = data[test_idx, 8]

for k in k_values:
    print(f"Testing k={k}")
    predictions = []

    for i in tqdm(range(len(x_test))):
        distances = []
        for j in range(len(x_train)):
            diff = x_test[i] - x_train[j]
            dist = np.sqrt(np.dot(diff, diff))
            distances.append((j, dist))

        distances.sort(key=lambda x: x[1])
        k_nearest = distances[:k]

        votes = 0
        for neighbor in k_nearest:
            if y_train[neighbor[0]] == 1:
                votes += 1

        prediction = 1 if votes > k / 2 else 0
        predictions.append(prediction)

    accuracy = np.mean(np.array(predictions) == y_test)

    # Store results
    results.append({
        'k': k,
        'accuracy': accuracy,
        'train_size': len(train_idx),
        'test_size': len(test_idx),
        'train_start_idx': min(train_idx),
        'train_end_idx': max(train_idx),
        'test_start_idx': min(test_idx),
        'test_end_idx': max(test_idx)
    })

    print(f"k={k}, Accuracy: {accuracy:.4f}")

# Save to CSV
results_df = pd.DataFrame(results)
results_df.to_csv('knn_results.csv', index=False)
print(f"\nResults saved to knn_results.csv")
print(results_df)