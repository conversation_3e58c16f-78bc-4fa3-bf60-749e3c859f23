import csv, pandas
import numpy as np
from tqdm import tqdm


data = pandas.read_csv("pima-indians-diabetes.csv", header=None)
#Data[Kol][rad/person]

# Tar du gravid[i] så er i = person gravid
ganger_gravid = data[0]
plasma = data[1]
bloodpressure = data[2]
skin = data[3]
insulin = data[4]
bmi = data[5]
pedigree = data[6]
age = data[7]
diabetes = data[8]

k=100


alle_distanser = []

for valgt_person in tqdm(range(len(data))):
    distanse = []
    for andre_person in range(len(data)):
        if valgt_person == andre_person:
            continue
        diff_gravid = (ganger_gravid[valgt_person] - ganger_gravid[andre_person])**2
        diff_plasma = (plasma[valgt_person] - plasma[andre_person])**2
        diff_bloodpressure = (bloodpressure[valgt_person] - bloodpressure[andre_person])**2
        diff_skin = (skin[valgt_person] - skin[andre_person])**2
        diff_insulin = (insulin[valgt_person] - insulin[andre_person])**2
        diff_bmi = (bmi[valgt_person] - bmi[andre_person])**2
        diff_pedigree = (pedigree[valgt_person] - pedigree[andre_person])**2
        diff_age = (age[valgt_person] - age[andre_person])**2
        distanse.append((andre_person, (
                    diff_gravid + diff_plasma + diff_bloodpressure + diff_skin + diff_insulin + diff_bmi + diff_pedigree + diff_age) ** 0.5))

    alle_distanser.append((valgt_person, distanse))

alle_naboer = []

for valgt_person, distanser in tqdm(alle_distanser):
    # sorter distansene for valgt_person
    distanser.sort(key=lambda x: x[1])

    # hent de k nærmeste
    naboer = distanser[:k]

    # legg til diabetes-status
    naboer_med_label = []
    for andre_person, avstand in naboer:
        label = diabetes[andre_person]  # 0 eller 1
        naboer_med_label.append((andre_person, avstand, label))

    # lagre resultatet
    alle_naboer.append((valgt_person, naboer_med_label))

prediksjoner = []

for valgt_person, naboer_med_label in tqdm(alle_naboer):
    # tell hvor mange av de k nærmeste som har diabetes
    antall_diabetes = sum(label for _, _, label in naboer_med_label)

    # prediksjon = 1 hvis majoriteten har diabetes, ellers 0
    prediksjon = 1 if antall_diabetes > k / 2 else 0
    prediksjoner.append(prediksjon)


ekte_labels = list(diabetes)

riktige = sum(1 for pred, ekte in zip(prediksjoner, ekte_labels) if pred == ekte)
accuracy = riktige / len(ekte_labels)

print("Accuracy:", accuracy)

""""with open("alle_distanser.csv", "w", newline="") as f:    writer = csv.writer(f)    writer.writerow(["valgt_person", "andre_person", "avstand"])    for valgt_person, distanser in alle_distanser:        for andre_person, avstand in distanser:            writer.writerow([valgt_person, andre_person, avstand])"""

