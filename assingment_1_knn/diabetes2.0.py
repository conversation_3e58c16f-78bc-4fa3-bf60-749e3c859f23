import numpy as np
from tqdm import tqdm

data = np.loadtxt('pima-indians-diabetes.csv', delimiter=',')

# data[rad][kolonne]
y_train = data[:500, 8]  # diabetes
x_train = data[:500, 0:8]  # alle x-verdier

x_test = data[500:, 0:8]  # alle x-verdier
y_test = data[500:, 8]  # diabetes

predictions = []
k=5

for i in tqdm[int](range(len(x_test))):
    distances = []
    for j in range(len(x_train)):
        diff = x_test[i] - x_train[j]
        dist = np.sqrt(np.dot(diff, diff))
        distances.append((j, dist))
    # Sort and get k nearest
    distances.sort(key=lambda x: x[1])
    k_nearest = distances[:k]

    votes = 0
    for i in k_nearest:
        if(y_train[i[0]]) ==1:
            votes += 1
    if votes > k/2:
        predictions.append(1)
    else:
        predictions.append(0)

accuracy = np.mean(predictions == y_test)
print(accuracy)